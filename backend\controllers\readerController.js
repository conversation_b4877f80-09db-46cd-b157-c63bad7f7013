
import { ReadingList, Book, User, BookListEntry, Favorite, Genre } from '../models/index.js';
import { sequelize } from '../models/index.js';

/**
 * Récupère toutes les listes de lecture de l'utilisateur et rend la vue.
 */
export async function getReadingLists(req, res) {
    try {
        const userId = req.user.id;
        // Pour l'instant, nous n'implémentons pas la pagination/recherche complexe ici,
        // car la vue a été simplifiée pour afficher des cartes.
        // findByUserIdWithBooks attend un objet options.
        const readingLists = await ReadingList.findByUserIdWithBooks(userId, {});

        res.render('auth/reading-lists', {
            title: 'Mes listes de lecture',
            readingLists,
            currentUser: req.user,
            // messages de flash potentiels
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des listes de lecture:', error);
        req.flash('error', 'Une erreur est survenue lors de la récupération de vos listes de lecture.');
        res.redirect('/dashboard'); // Ou une page d'erreur appropriée
    }
}

/**
 * API: Récupère toutes les listes de lecture de l'utilisateur en JSON
 */
export async function getReadingListsAPI(req, res) {
    try {
        const userId = req.user.id;
        const readingLists = await ReadingList.findByUserIdWithBooks(userId, {});

        res.json({
            success: true,
            data: readingLists
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des listes de lecture (API):', error);
        res.status(500).json({
            success: false,
            message: 'Une erreur est survenue lors de la récupération de vos listes de lecture.'
        });
    }
}

/**
 * Affiche le formulaire de création d'une nouvelle liste de lecture
 */
export const newReadingListForm = async (req, res) => {
  try {
    res.render('auth/reading-list-form-temp', {
      title: 'Nouvelle liste de lecture - Nookli',
      currentUser: req.user,
      readingList: { list_name: '', description: '', is_public: false },
      formAction: '/dashboard/reading-lists',
      formTitle: 'Créer une nouvelle liste de lecture'
    });
  } catch (error) {
    console.error('Erreur lors de l\'affichage du formulaire:', error);
    req.flash('error', 'Une erreur est survenue lors de l\'affichage du formulaire.');
    res.redirect('/dashboard/reading-lists');
  }
};

/**
 * Affiche le formulaire d'édition d'une liste de lecture
 */
export async function renderEditReadingListForm(req, res) {
    const listId = req.params.id;
    const userId = req.user.id;

    try {
        // Récupérer la liste avec ses livres pour les métadonnées
        const readingList = await ReadingList.findOne({
            where: {
                id: listId,
                user_id: userId
            },
            include: [
                {
                    model: Book,
                    as: 'books',
                    attributes: ['id'] // Juste pour compter
                }
            ]
        });

        if (!readingList) {
            req.flash('error', 'Liste de lecture non trouvée.');
            return res.redirect('/dashboard/reading-lists');
        }

        // Vérifier si c'est une liste par défaut
        if (readingList.is_default) {
            req.flash('error', 'Les listes par défaut ne peuvent pas être modifiées.');
            return res.redirect('/dashboard/reading-lists');
        }

        res.render('auth/reading-list-form-temp', {
            title: 'Modifier la liste de lecture - Nookli',
            currentUser: req.user,
            formTitle: 'Modifier la liste de lecture',
            formAction: `/dashboard/reading-lists/edit/${listId}`,
            readingList: readingList
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de la liste de lecture:', error);
        req.flash('error', 'Une erreur est survenue lors de la récupération de la liste.');
        res.redirect('/dashboard/reading-lists');
    }
}


/**
 * Récupère une liste de lecture spécifique avec ses livres
 */
export async function getReadingList(req, res) {
    try {
        const userId = req.user.id;
        const listId = req.params.id;
        const search = req.query.search || '';
        const sort = req.query.sort || 'title_asc';

        // Vérifier que la liste appartient à l'utilisateur
        const list = await ReadingList.findByIdAndUserId(listId, userId);
        if (!list) {
            return res.status(404).json({
                success: false,
                message: 'Liste de lecture non trouvée'
            });
        }

        // Définir l'ordre de tri
        let order = [];
        switch (sort) {
            case 'title_asc':
                order = [[{ model: Book, as: 'books' }, 'title', 'ASC']];
                break;
            case 'title_desc':
                order = [[{ model: Book, as: 'books' }, 'title', 'DESC']];
                break;
            case 'author_asc':
                order = [[{ model: Book, as: 'books' }, 'author', 'ASC']];
                break;
            case 'author_desc':
                order = [[{ model: Book, as: 'books' }, 'author', 'DESC']];
                break;
            case 'date_added_asc':
                order = [[{ model: Book, as: 'books' }, BookListEntry, 'created_at', 'ASC']];
                break;
            case 'date_added_desc':
                order = [[{ model: Book, as: 'books' }, BookListEntry, 'created_at', 'DESC']];
                break;
            default:
                order = [[{ model: Book, as: 'books' }, 'title', 'ASC']];
        }

        // Construire la condition de recherche
        const whereCondition = {};
        if (search) {
            whereCondition[sequelize.Op.or] = [
                { title: { [sequelize.Op.like]: `%${search}%` } },
                { author: { [sequelize.Op.like]: `%${search}%` } },
                { isbn: { [sequelize.Op.like]: `%${search}%` } }
            ];
        }

        // Récupérer la liste avec ses livres
        const listWithBooks = await ReadingList.findOne({
            where: { id: listId },
            include: [
                {
                    model: Book,
                    as: 'books',
                    where: search ? whereCondition : {},
                    required: search ? true : false,
                    include: [
                        {
                            model: Genre,
                            as: 'genre',
                            attributes: ['id', 'name']
                        }
                    ]
                }
            ],
            order
        });

        res.json({
            success: true,
            data: listWithBooks
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de la liste de lecture:', error);
        res.status(500).json({
            success: false,
            message: 'Une erreur est survenue lors de la récupération de la liste de lecture'
        });
    }
}

/**
 * Crée une nouvelle liste de lecture
 */
export const createReadingList = async (req, res) => {
  try {
    const { list_name, description, is_public } = req.body;
    
    // Validation de base
    if (!list_name || list_name.trim() === '') {
      req.flash('error', 'Le nom de la liste est requis');
      return res.redirect('/dashboard/reading-lists/new');
    }
    
    // Créer la liste
    await ReadingList.create({
      user_id: req.user.id,
      list_name: list_name.trim(),
      description: description ? description.trim() : null,
      is_default: false,
      is_public: is_public === 'true'
    });
    
    req.flash('success', 'Liste de lecture créée avec succès');
    res.redirect('/dashboard/reading-lists');
  } catch (error) {
    console.error('Erreur lors de la création de la liste:', error);
    req.flash('error', 'Une erreur est survenue lors de la création de la liste');
    res.redirect('/dashboard/reading-lists/new');
  }
};

/**
 * Met à jour le nom et la description d'une liste de lecture
 */
export async function updateReadingList(req, res) {
    try {
        const userId = req.user.id;
        const listId = req.params.id;
        const { list_name, description, is_public } = req.body;

        if (!list_name) {
            req.flash('error', 'Le nom de la liste est requis.');
            return res.redirect(`/dashboard/reading-lists/edit/${listId}`);
        }

        // Vérifier que la liste appartient à l'utilisateur
        const list = await ReadingList.findByIdAndUserId(listId, userId);
        if (!list) {
            req.flash('error', 'Liste de lecture non trouvée.');
            return res.redirect('/dashboard/reading-lists');
        }

        // Vérifier si c'est une liste par défaut
        if (list.is_default) {
            req.flash('error', 'Les listes par défaut ne peuvent pas être modifiées.');
            return res.redirect('/dashboard/reading-lists');
        }

        // Vérifier si une autre liste avec ce nom existe déjà pour cet utilisateur
        const existingList = await ReadingList.findOne({
            where: {
                user_id: userId,
                list_name,
                id: { [sequelize.Op.ne]: listId }
            }
        });

        if (existingList) {
            req.flash('error', 'Une autre liste avec ce nom existe déjà.');
            return res.redirect(`/dashboard/reading-lists/edit/${listId}`);
        }

        // Mettre à jour la liste
        list.list_name = list_name;
        list.description = description || null;
        list.is_public = is_public === 'true';
        await list.save();

        req.flash('success', 'Liste de lecture mise à jour avec succès !');
        res.redirect('/dashboard/reading-lists');
    } catch (error) {
        console.error('Erreur lors de la mise à jour de la liste de lecture:', error);
        req.flash('error', 'Une erreur est survenue lors de la mise à jour.');
        res.redirect(`/dashboard/reading-lists/edit/${listId}`);
    }
}

/**
 * Supprime une liste de lecture
 */
export async function deleteReadingList(req, res) {
    try {
        const userId = req.user.id;
        const listId = req.params.id;

        // Vérifier que la liste appartient à l'utilisateur
        const list = await ReadingList.findByIdAndUserId(listId, userId);
        if (!list) {
            req.flash('error', 'Liste de lecture non trouvée.');
            return res.redirect('/dashboard/reading-lists');
        }

        // Vérifier si c'est une liste par défaut
        if (list.is_default) {
            req.flash('error', 'Les listes par défaut ne peuvent pas être supprimées.');
            return res.redirect('/dashboard/reading-lists');
        }

        // Supprimer la liste
        await list.destroy();

        req.flash('success', 'Liste de lecture supprimée avec succès.');
        res.redirect('/dashboard/reading-lists');
    } catch (error) {
        console.error('Erreur lors de la suppression de la liste de lecture:', error);
        req.flash('error', 'Une erreur est survenue lors de la suppression.');
        res.redirect('/dashboard/reading-lists');
    }
}

/**
 * Ajoute un livre à une liste de lecture
 */
export async function addBookToList(req, res) {
    try {
        const userId = req.user.id;
        const { list_id, book_id } = req.body;

        if (!list_id || !book_id) {
            return res.status(400).json({
                success: false,
                message: 'L\'ID de la liste et l\'ID du livre sont requis'
            });
        }

        // Vérifier que la liste appartient à l'utilisateur
        const list = await ReadingList.findByIdAndUserId(list_id, userId);
        if (!list) {
            return res.status(404).json({
                success: false,
                message: 'Liste de lecture non trouvée'
            });
        }

        // Vérifier que le livre existe
        const book = await Book.findByPk(book_id);
        if (!book) {
            return res.status(404).json({
                success: false,
                message: 'Livre non trouvé'
            });
        }

        // Ajouter le livre à la liste
        await BookListEntry.addBookToList(list_id, book_id);

        res.json({
            success: true,
            message: 'Livre ajouté à la liste avec succès'
        });
    } catch (error) {
        console.error('Erreur lors de l\'ajout du livre à la liste:', error);
        res.status(500).json({
            success: false,
            message: 'Une erreur est survenue lors de l\'ajout du livre à la liste'
        });
    }
}

/**
 * Supprime un livre d'une liste de lecture
 */
export async function removeBookFromList(req, res) {
    try {
        const userId = req.user.id;
        const listId = req.params.listId;
        const bookId = req.params.bookId;

        // Vérifier que la liste appartient à l'utilisateur
        const list = await ReadingList.findByIdAndUserId(listId, userId);
        if (!list) {
            return res.status(404).json({
                success: false,
                message: 'Liste de lecture non trouvée'
            });
        }

        // Supprimer le livre de la liste
        const removed = await BookListEntry.removeBookFromList(listId, bookId);
        if (!removed) {
            return res.status(404).json({
                success: false,
                message: 'Le livre n\'est pas dans cette liste'
            });
        }

        res.json({
            success: true,
            message: 'Livre retiré de la liste avec succès'
        });
    } catch (error) {
        console.error('Erreur lors de la suppression du livre de la liste:', error);
        res.status(500).json({
            success: false,
            message: 'Une erreur est survenue lors de la suppression du livre de la liste'
        });
    }
}

/**
 * Récupère les favoris de l'utilisateur avec options de tri, recherche et pagination
 */
export async function getFavorites(req, res) {
    try {
        const userId = req.user.id;
        const search = req.query.search || '';
        const sort = req.query.sort || 'title_asc';
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || 20;

        // Récupérer les favoris avec filtres et pagination en utilisant la méthode du modèle
        const favorites = await Favorite.findByUserId(userId, {
            search,
            sort,
            page,
            pageSize
        });

        // Compter le nombre total de favoris pour la pagination
        const totalFavorites = await Favorite.count({
            where: { user_id: userId }
        });

        // Calculer le nombre total de pages
        const totalPages = Math.ceil(totalFavorites / pageSize);

        // Rendu de la page ou réponse API selon le type de requête
        if (req.xhr || req.headers.accept.includes('application/json')) {
            res.json({
                success: true,
                data: {
                    favorites: favorites.map(fav => fav.book),
                    pagination: {
                        page,
                        pageSize,
                        totalItems: totalFavorites,
                        totalPages
                    }
                }
            });
        } else {
            res.render('auth/favorites', {
                favorites: favorites.map(fav => fav.book),
                currentUser: req.user,
                path: '/dashboard/favorites',
                pagination: {
                    page,
                    pageSize,
                    totalItems: totalFavorites,
                    totalPages
                }
            });
        }
    } catch (error) {
        console.error('Erreur lors de la récupération des favoris:', error);

        if (req.xhr || req.headers.accept.includes('application/json')) {
            res.status(500).json({
                success: false,
                message: 'Une erreur est survenue lors de la récupération des favoris'
            });
        } else {
            res.render('auth/favorites', {
                favorites: [],
                currentUser: req.user,
                path: '/dashboard/favorites',
                error: 'Une erreur est survenue lors de la récupération des favoris'
            });
        }
    }
}

/**
 * Ajoute un livre aux favoris
 */
export async function addFavorite(req, res) {
    try {
        console.log('=== ADD FAVORITE DEBUG ===');
        console.log('User:', req.user ? req.user.id : 'Non connecté');
        console.log('Body:', req.body);
        console.log('Headers:', req.headers);

        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Utilisateur non authentifié'
            });
        }

        const userId = req.user.id;
        const { book_id } = req.body;

        console.log('UserId:', userId, 'BookId:', book_id);

        if (!book_id) {
            return res.status(400).json({
                success: false,
                message: 'L\'ID du livre est requis'
            });
        }

        // Vérifier que le livre existe
        const book = await Book.findByPk(book_id);
        if (!book) {
            return res.status(404).json({
                success: false,
                message: 'Livre non trouvé'
            });
        }

        console.log('Livre trouvé:', book.title);

        // Ajouter le livre aux favoris
        await Favorite.addFavorite(userId, book_id);

        console.log('Livre ajouté aux favoris avec succès');

        res.json({
            success: true,
            message: 'Livre ajouté aux favoris avec succès'
        });
    } catch (error) {
        console.error('Erreur lors de l\'ajout du livre aux favoris:', error);
        res.status(500).json({
            success: false,
            message: 'Une erreur est survenue lors de l\'ajout du livre aux favoris'
        });
    }
}

/**
 * Vérifie si un livre spécifique est dans les favoris de l'utilisateur
 */
export async function checkFavoriteStatus(req, res) {
    try {
        const userId = req.user.id;
        const bookId = req.params.bookId;

        console.log('=== CHECK FAVORITE STATUS DEBUG ===');
        console.log('UserId:', userId, 'BookId:', bookId);

        // Vérifier que le livre existe
        const book = await Book.findByPk(bookId);
        if (!book) {
            return res.status(404).json({
                success: false,
                message: 'Livre non trouvé'
            });
        }

        // Vérifier si le livre est dans les favoris
        const isFavorite = await Favorite.isFavorite(userId, bookId);

        console.log('Statut favori vérifié:', isFavorite);

        res.json({
            success: true,
            isFavorite,
            message: isFavorite ? 'Le livre est dans vos favoris' : 'Le livre n\'est pas dans vos favoris'
        });
    } catch (error) {
        console.error('Erreur lors de la vérification du statut favori:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur lors de la vérification du statut favori'
        });
    }
}

/**
 * Supprime un livre des favoris
 */
export async function removeFavorite(req, res) {
    try {
        const userId = req.user.id;
        const bookId = req.params.bookId;

        // Supprimer le livre des favoris
        const removed = await Favorite.removeFavorite(userId, bookId);
        if (!removed) {
            return res.status(404).json({
                success: false,
                message: 'Le livre n\'est pas dans vos favoris'
            });
        }

        res.json({
            success: true,
            message: 'Livre retiré des favoris avec succès'
        });
    } catch (error) {
        console.error('Erreur lors de la suppression du livre des favoris:', error);
        res.status(500).json({
            success: false,
            message: 'Une erreur est survenue lors de la suppression du livre des favoris'
        });
    }
}

// Exporter toutes les fonctions
export default {
    getReadingLists,
    newReadingListForm,
    renderEditReadingListForm,
    editReadingListForm: renderEditReadingListForm,
    getReadingList,
    getReadingListDetails: getReadingList,
    createReadingList,
    updateReadingList,
    deleteReadingList,
    addBookToList,
    removeBookFromList,
    getFavorites,
    addFavorite,
    checkFavoriteStatus,
    removeFavorite
};


