<!-- Modal pour ajouter à une liste de lecture -->
<div class="modal" id="addToListModal">
    <div class="modal-content">
        <span class="close" id="closeAddToListModal">&times;</span>
        <h3>Ajouter à une liste de lecture</h3>
        
        <div id="addToListContainer">
            <!-- Les listes seront chargées dynamiquement ici -->
            <div class="loading-spinner" id="listsLoadingSpinner">
                <i class="fas fa-spinner fa-spin"></i> Chargement des listes...
            </div>
        </div>
        
        <div class="form-actions">
            <button type="button" class="btn btn-secondary" id="cancelAddToListBtn">Fermer</button>
        </div>
    </div>
</div>

<style>
/* Styles pour la modal d'ajout à une liste */
.reading-lists-select {
    max-height: 300px;
    overflow-y: auto;
    margin: 20px 0;
}

.list-select-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 8px;
    background-color: #f9f9f9;
    transition: background-color 0.2s ease;
}

.list-select-item:hover {
    background-color: #f0f0f0;
}

.list-select-item span {
    font-weight: 500;
    color: #333;
    flex-grow: 1;
}

.list-select-item .list-book-count {
    font-size: 0.9em;
    color: #666;
    margin-left: 10px;
}

.add-to-list-btn {
    min-width: 80px;
    padding: 6px 12px;
    font-size: 0.9em;
}

.add-to-list-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state i {
    font-size: 3em;
    color: #ccc;
    margin-bottom: 15px;
}

.loading-spinner {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.loading-spinner i {
    margin-right: 10px;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 8px;
    margin: 10px 0;
    border: 1px solid #f5c6cb;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 12px;
    border-radius: 8px;
    margin: 10px 0;
    border: 1px solid #c3e6cb;
}

/* Animation pour les boutons de chargement */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>
