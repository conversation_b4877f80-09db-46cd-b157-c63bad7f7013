/**
 * Module pour la gestion des listes de lecture
 */
import API from '../api.js';

const ReadingListsManager = {
    // Flag pour éviter la duplication des event listeners
    modalListenersInitialized: false,

    /**
     * Initialise le gestionnaire de listes de lecture
     */
    init() {
        console.log('ReadingListsManager.init() appelé');
        
        // Éléments DOM pour le dashboard
        this.tabsContainer = document.querySelector('.dashboard-tabs');
        if (this.tabsContainer) {
            this.initDashboardTabs();
            this.initReadingListsTabs();
        }
        
        // Toujours initialiser les actions (création, modification, suppression)
        this.initListActions();

        // Éléments DOM pour la page de détails du livre
        this.bookDetailsPage = document.querySelector('.book-details');
        if (this.bookDetailsPage) {
            this.initBookDetailsPage();
        }

        // Éléments DOM pour la page de détails d'une liste de lecture
        this.listDetailsPage = document.querySelector('#list-books-container');
        if (this.listDetailsPage) {
            this.initListDetailsPage();
        }

        // Initialiser les boutons d'ajout à une liste sur toutes les pages
        this.initAddToListButtons();
    },

    /**
     * Initialise tous les boutons "Ajouter à une liste" sur la page
     */
    initAddToListButtons() {
        const addToListButtons = document.querySelectorAll('#addToListBtn, .add-to-list-btn');

        addToListButtons.forEach(button => {
            // Éviter de dupliquer les event listeners
            if (button.dataset.initialized === 'true') {
                return;
            }

            button.addEventListener('click', (e) => {
                e.preventDefault();

                // Vérifier si l'utilisateur est connecté
                if (!this.isUserLoggedIn()) {
                    console.warn('Utilisateur non connecté - redirection vers login');
                    window.location.href = '/login';
                    return;
                }

                // Récupérer l'ID du livre
                const bookId = this.getBookIdFromButton(button);
                if (bookId) {
                    this.showAddToListModal(bookId);
                } else {
                    console.error('ID du livre non trouvé');
                    this.showNotification('Erreur: ID du livre non trouvé', 'error');
                }
            });

            // Marquer le bouton comme initialisé
            button.dataset.initialized = 'true';
        });
    },

    /**
     * Récupère l'ID du livre depuis un bouton
     * @param {HTMLElement} button - Le bouton
     * @returns {number|null} - L'ID du livre ou null
     */
    getBookIdFromButton(button) {
        // Essayer de récupérer l'ID depuis les data attributes
        if (button.dataset.bookId) {
            return parseInt(button.dataset.bookId);
        }

        // Essayer de récupérer l'ID depuis l'URL si on est sur une page de détails de livre
        const bookId = this.getBookIdFromUrl();
        if (bookId) {
            return bookId;
        }

        // Essayer de trouver l'ID dans le contexte parent
        const bookCard = button.closest('.book-card');
        if (bookCard) {
            const bookLink = bookCard.querySelector('a[href*="/books/"]');
            if (bookLink) {
                const match = bookLink.href.match(/\/books\/(\d+)/);
                return match ? parseInt(match[1]) : null;
            }
        }

        return null;
    },

    /**
     * Initialise les onglets du dashboard
     */
    initDashboardTabs() {
        const tabButtons = this.tabsContainer.querySelectorAll('.tab-button');
        const tabPanes = this.tabsContainer.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Désactiver tous les onglets
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // Activer l'onglet cliqué
                button.classList.add('active');
                const tabId = button.dataset.tab;
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });
    },

    /**
     * Initialise les onglets des listes de lecture
     */
    initReadingListsTabs() {
        const listTabs = document.querySelectorAll('.tab-btn');
        const listContents = document.querySelectorAll('.tab-content');

        listTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Désactiver tous les onglets
                listTabs.forEach(t => t.classList.remove('active'));
                listContents.forEach(c => c.classList.remove('active'));

                // Activer l'onglet cliqué
                tab.classList.add('active');
                const listId = tab.dataset.listId;
                document.getElementById(`list-${listId}`).classList.add('active');
            });
        });
    },

    /**
     * Initialise les actions sur les listes (créer, renommer, supprimer)
     */
    initListActions() {
        console.log('Initialisation des actions sur les listes...');
        
        // Bouton pour créer une nouvelle liste
        const createListBtn = document.getElementById('createListBtn');
        console.log('Bouton createListBtn trouvé:', createListBtn);
        
        if (createListBtn) {
            createListBtn.addEventListener('click', this.showCreateListModal.bind(this));
            console.log('Event listener ajouté sur createListBtn');
        }

        // Gérer aussi le bouton de l'état vide s'il existe
        const createListBtnEmpty = document.getElementById('createListBtnEmpty');
        if (createListBtnEmpty) {
            createListBtnEmpty.addEventListener('click', this.showCreateListModal.bind(this));
            console.log('Event listener ajouté sur createListBtnEmpty');
        }

        // Boutons pour renommer une liste
        const renameButtons = document.querySelectorAll('.rename-list');
        renameButtons.forEach(button => {
            button.addEventListener('click', () => {
                const listId = button.dataset.listId;
                const listName = button.dataset.listName;
                this.showRenameListModal(listId, listName);
            });
        });

        // Boutons pour supprimer une liste
        const deleteButtons = document.querySelectorAll('.delete-list');
        deleteButtons.forEach(button => {
            button.addEventListener('click', () => {
                const listId = button.dataset.listId;
                this.confirmDeleteList(listId);
            });
        });

        // Boutons pour retirer un livre d'une liste
        const removeButtons = document.querySelectorAll('.remove-from-list');
        removeButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                const listId = button.dataset.listId;
                const bookId = button.dataset.bookId;
                await this.removeBookFromList(listId, bookId);
            });
        });

        // Modal de liste
        const modal = document.getElementById('listModal');
        if (modal) {
            // Fermer la modal
            const closeBtn = modal.querySelector('.close');
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });

            const cancelBtn = document.getElementById('cancelListBtn');
            cancelBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });

            // Formulaire de liste
            const listForm = document.getElementById('listForm');
            if (listForm) {
                listForm.addEventListener('submit', this.handleListFormSubmit.bind(this));
            }
        }
    },

    /**
     * Initialise les event listeners de la modal (une seule fois)
     */
    initModalEventListeners() {
        if (this.modalListenersInitialized) {
            return; // Éviter la duplication
        }

        const modal = document.getElementById('listModal');
        if (!modal) return;

        // Fermer la modal avec le bouton X
        const closeBtn = modal.querySelector('.close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
            });
        }

        // Fermer la modal avec le bouton Annuler
        const cancelBtn = document.getElementById('cancelListBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
            });
        }

        // Fermer en cliquant à l'extérieur de la modal
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
            }
        });

        this.modalListenersInitialized = true;
    },

    /**
     * Initialise les actions sur la page de détails du livre
     */
    initBookDetailsPage() {
        const bookId = this.getBookIdFromUrl();
        if (!bookId) return;

        // Bouton pour ajouter à une liste
        const addToListBtn = document.getElementById('addToListBtn');
        console.log('DEBUG: addToListBtn trouvé:', addToListBtn);
        if (addToListBtn) {
            addToListBtn.addEventListener('click', () => {
                console.log('DEBUG: Clic sur le bouton d\'ajout à une liste détecté');
                // Vérifier si l'utilisateur est connecté avant d'appeler l'API
                if (this.isUserLoggedIn()) {
                    console.log('DEBUG: Utilisateur connecté, ouverture de la modal');
                    this.showAddToListModal(bookId);
                } else {
                    console.warn('Utilisateur non connecté - redirection vers login');
                }
            });
        } else {
            console.error('DEBUG: Bouton addToListBtn non trouvé !');
        }

        // Initialiser les event listeners de la modal (une seule fois)
        this.initModalEventListeners();
    },

    /**
     * Vérifie si l'utilisateur est connecté
     * @returns {boolean} - true si l'utilisateur est connecté, false sinon
     */
    isUserLoggedIn() {
        // Vérifier la présence d'éléments qui indiquent que l'utilisateur est connecté
        return document.querySelector('#addToListBtn') !== null &&
               !document.querySelector('.book-actions-guest');
    },

    /**
     * Récupère l'ID du livre depuis l'URL
     * @returns {number|null} - ID du livre ou null
     */
    getBookIdFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/books\/(\d+)/);
        return match ? parseInt(match[1]) : null;
    },

    /**
     * Charge les livres d'une liste avec options de tri et recherche
     * @param {number} listId - ID de la liste
     * @param {string} search - Terme de recherche
     * @param {string} sort - Option de tri
     */
    async loadListBooks(listId, search = '', sort = 'title_asc') {
        try {
            const response = await API.getReadingList(listId, search, sort);
            const list = response.data;

            // Mettre à jour l'affichage des livres
            const listContent = document.getElementById(`list-${listId}`);
            if (!listContent) return;

            const booksContainer = listContent.querySelector('.books-grid') || listContent.querySelector('.empty-state');
            if (!booksContainer) return;

            if (!list.books || list.books.length === 0) {
                booksContainer.innerHTML = `
                    <div class="empty-state">
                        <p>Aucun livre dans cette liste${search ? ' correspondant à votre recherche' : ''}.</p>
                        <a href="/books" class="btn btn-secondary">Explorer le catalogue</a>
                    </div>
                `;
                return;
            }

            let html = '<div class="books-grid small">';
            list.books.forEach(book => {
                html += `
                    <div class="book-card">
                        <a href="/books/${book.id}" class="book-link">
                            <div class="book-cover">
                                <img src="${book.cover_image_url || 'https://via.placeholder.com/300x450?text=Nookli'}" alt="Couverture de ${book.title}">
                            </div>
                            <div class="book-info">
                                <h4>${book.title}</h4>
                                <p class="book-author">${book.author}</p>
                            </div>
                        </a>
                        <button class="btn btn-text remove-from-list" data-list-id="${listId}" data-book-id="${book.id}">
                            <i class="fas fa-times"></i> Retirer
                        </button>
                    </div>
                `;
            });
            html += '</div>';

            booksContainer.outerHTML = html;

            // Ajouter les événements aux boutons de suppression
            const removeButtons = listContent.querySelectorAll('.remove-from-list');
            removeButtons.forEach(button => {
                button.addEventListener('click', async (e) => {
                    e.preventDefault();
                    const listId = button.dataset.listId;
                    const bookId = button.dataset.bookId;
                    await this.removeBookFromList(listId, bookId);
                });
            });
        } catch (error) {
            console.error('Erreur lors du chargement des livres de la liste:', error);
        }
    },

    /**
     * Affiche la modal pour créer une nouvelle liste
     */
    showCreateListModal() {
        console.log('showCreateListModal appelée');
        
        const modal = document.getElementById('listModal');
        console.log('Modal trouvée:', modal);
        
        if (!modal) {
            console.error('Modal listModal non trouvée!');
            return;
        }
        
        const modalTitle = document.getElementById('modalTitle');
        const listForm = document.getElementById('listForm');
        const listIdInput = document.getElementById('listId');
        const listNameInput = document.getElementById('listName');

        if (modalTitle) modalTitle.textContent = 'Nouvelle liste de lecture';
        if (listIdInput) listIdInput.value = '';
        if (listNameInput) listNameInput.value = '';
        if (listForm) listForm.dataset.action = 'create';

        modal.style.display = 'block';
        console.log('Modal affichée');
    },

    /**
     * Affiche la modal pour renommer une liste
     * @param {number} listId - ID de la liste
     * @param {string} listName - Nom actuel de la liste
     */
    showRenameListModal(listId, listName) {
        const modal = document.getElementById('listModal');
        const modalTitle = document.getElementById('modalTitle');
        const listForm = document.getElementById('listForm');
        const listIdInput = document.getElementById('listId');
        const listNameInput = document.getElementById('listName');

        modalTitle.textContent = 'Renommer la liste';
        listIdInput.value = listId;
        listNameInput.value = listName;
        listForm.dataset.action = 'rename';

        modal.style.display = 'block';
    },

    /**
     * Affiche la modal pour ajouter un livre à une liste
     * @param {number} bookId - ID du livre
     */
    async showAddToListModal(bookId) {
        const modal = document.getElementById('listModal');
        const container = document.getElementById('reading-lists-container');

        if (!modal || !container) {
            console.error('Modal ou container non trouvé');
            return;
        }

        // Afficher la modal avec le spinner de chargement
        modal.style.display = 'block';
        // Ajouter la classe 'show' pour l'animation CSS
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        container.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i> Chargement des listes...
            </div>
        `;

        try {
            // Récupérer les listes de l'utilisateur
            const response = await API.getReadingLists();
            const lists = response.data;

            // Générer le HTML pour les listes
            let html = '';
            if (lists.length === 0) {
                html = `
                    <div class="empty-state">
                        <i class="fas fa-list"></i>
                        <p>Vous n'avez pas encore de liste de lecture.</p>
                        <p><a href="/dashboard/reading-lists/new" class="btn btn-primary">Créer ma première liste</a></p>
                    </div>
                `;
            } else {
                html = '<div class="reading-lists-select">';
                lists.forEach(list => {
                    const bookCount = list.books ? list.books.length : 0;
                    html += `
                        <div class="list-select-item">
                            <div>
                                <span>${list.list_name}</span>
                                <span class="list-book-count">(${bookCount} livre${bookCount > 1 ? 's' : ''})</span>
                            </div>
                            <button class="btn btn-primary add-to-list-btn" data-list-id="${list.id}" data-book-id="${bookId}">
                                Ajouter
                            </button>
                        </div>
                    `;
                });
                html += '</div>';
            }

            container.innerHTML = html;

            // Ajouter les événements aux boutons
            const addButtons = container.querySelectorAll('.add-to-list-btn');
            addButtons.forEach(button => {
                button.addEventListener('click', async (e) => {
                    e.preventDefault();
                    const listId = button.dataset.listId;
                    const bookId = button.dataset.bookId;

                    // Désactiver le bouton et afficher le spinner
                    button.disabled = true;
                    button.classList.add('btn-loading');

                    try {
                        await this.addBookToList(listId, bookId);
                        // Fermer la modal après succès avec animation
                        modal.classList.remove('show');
                        setTimeout(() => {
                            modal.style.display = 'none';
                        }, 300);
                    } catch (error) {
                        // Réactiver le bouton en cas d'erreur
                        button.disabled = false;
                        button.classList.remove('btn-loading');
                    }
                });
            });

            // Les event listeners de la modal sont déjà configurés dans initModalEventListeners

        } catch (error) {
            console.error('Erreur lors du chargement des listes:', error);
            container.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    Erreur lors du chargement des listes: ${error.message}
                </div>
            `;
        }
    },

    /**
     * Gère la soumission du formulaire de liste
     * @param {Event} e - Événement de soumission
     */
    async handleListFormSubmit(e) {
        e.preventDefault();
        const form = e.currentTarget;
        const action = form.dataset.action;
        const listId = document.getElementById('listId').value;
        const listName = document.getElementById('listName').value;
        const listDescription = document.getElementById('listDescription') ? document.getElementById('listDescription').value : '';

        try {
            if (action === 'create') {
                await API.createReadingList(listName, listDescription);
                window.location.reload(); // Recharger la page pour voir la nouvelle liste
            } else if (action === 'rename') {
                await API.updateReadingList(listId, listName);
                window.location.reload(); // Recharger la page pour voir le nouveau nom
            }
        } catch (error) {
            alert(`Erreur: ${error.message}`);
        }
    },

    /**
     * Demande confirmation avant de supprimer une liste
     * @param {number} listId - ID de la liste
     */
    confirmDeleteList(listId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette liste ? Cette action est irréversible.')) {
            this.deleteList(listId);
        }
    },

    /**
     * Supprime une liste
     * @param {number} listId - ID de la liste
     */
    async deleteList(listId) {
        try {
            await API.deleteReadingList(listId);
            window.location.reload(); // Recharger la page pour voir les changements
        } catch (error) {
            alert(`Erreur: ${error.message}`);
        }
    },



    /**
     * Ajoute un livre à une liste
     * @param {number} listId - ID de la liste
     * @param {number} bookId - ID du livre
     */
    async addBookToList(listId, bookId) {
        try {
            const response = await API.addBookToList(listId, bookId);

            // Afficher une notification de succès
            this.showNotification('Livre ajouté à la liste avec succès !', 'success');

            // Émettre un événement personnalisé pour informer les autres composants
            window.dispatchEvent(new CustomEvent('bookAddedToList', {
                detail: { listId, bookId, response }
            }));

        } catch (error) {
            console.error('Erreur lors de l\'ajout du livre à la liste:', error);
            this.showNotification(`Erreur: ${error.message}`, 'error');
            throw error; // Re-lancer l'erreur pour que l'appelant puisse la gérer
        }
    },

    /**
     * Affiche une notification à l'utilisateur
     * @param {string} message - Message à afficher
     * @param {string} type - Type de notification ('success', 'error', 'info')
     */
    showNotification(message, type = 'info') {
        // Créer l'élément de notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        `;

        // Ajouter les styles si ce n'est pas déjà fait
        if (!document.getElementById('notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'notification-styles';
            styles.textContent = `
                .notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    z-index: 10000;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    max-width: 400px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    animation: slideInRight 0.3s ease-out;
                }
                .notification-success { background-color: #28a745; }
                .notification-error { background-color: #dc3545; }
                .notification-info { background-color: #17a2b8; }
                .notification-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    margin-left: auto;
                }
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(styles);
        }

        // Ajouter la notification au DOM
        document.body.appendChild(notification);

        // Gérer la fermeture
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        });

        // Auto-fermeture après 5 secondes
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    },

    /**
     * Initialise la page de détails d'une liste de lecture
     */
    initListDetailsPage() {
        // Récupérer l'ID de la liste depuis l'URL
        const listId = this.getListIdFromUrl();
        if (!listId) return;

        // Initialiser les boutons de recherche et de tri
        this.initListDetailsSearch(listId);

        // Initialiser les boutons pour retirer des livres
        const removeButtons = document.querySelectorAll('.remove-from-list');
        removeButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                const listId = button.dataset.listId;
                const bookId = button.dataset.bookId;
                await this.removeBookFromList(listId, bookId);
            });
        });

        // Initialiser les boutons de renommage et suppression
        const renameButton = document.querySelector('.rename-list');
        if (renameButton) {
            renameButton.addEventListener('click', () => {
                const listId = renameButton.dataset.listId;
                const listName = renameButton.dataset.listName;
                this.showRenameListModal(listId, listName);
            });
        }

        const deleteButton = document.querySelector('.delete-list');
        if (deleteButton) {
            deleteButton.addEventListener('click', () => {
                const listId = deleteButton.dataset.listId;
                this.confirmDeleteList(listId);
            });
        }
    },

    /**
     * Initialise la recherche et le tri sur la page de détails d'une liste
     * @param {number} listId - ID de la liste
     */
    initListDetailsSearch(listId) {
        const searchInput = document.getElementById(`search-list-${listId}`);
        const searchBtn = document.querySelector(`.search-btn[data-list-id="${listId}"]`);
        const sortSelect = document.getElementById(`sort-list-${listId}`);
        const applyBtn = document.querySelector(`.apply-filters[data-list-id="${listId}"]`);
        const toggleBtn = document.querySelector(`.toggle-filters[data-list-id="${listId}"]`);
        const filtersContainer = document.getElementById(`filters-list-${listId}`);

        if (toggleBtn && filtersContainer) {
            toggleBtn.addEventListener('click', () => {
                filtersContainer.classList.toggle('show');
                const icon = toggleBtn.querySelector('i');
                if (filtersContainer.classList.contains('show')) {
                    icon.className = 'fas fa-chevron-up';
                } else {
                    icon.className = 'fas fa-chevron-down';
                }
            });
        }

        if (searchBtn && searchInput) {
            searchBtn.addEventListener('click', () => {
                const search = searchInput.value.trim();
                const sort = sortSelect ? sortSelect.value : 'title_asc';
                this.loadListBooks(listId, search, sort);
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const search = searchInput.value.trim();
                    const sort = sortSelect ? sortSelect.value : 'title_asc';
                    this.loadListBooks(listId, search, sort);
                }
            });
        }

        if (applyBtn && sortSelect) {
            applyBtn.addEventListener('click', () => {
                const search = searchInput ? searchInput.value.trim() : '';
                const sort = sortSelect.value;
                this.loadListBooks(listId, search, sort);
            });
        }
    },

    /**
     * Récupère l'ID de la liste depuis l'URL
     * @returns {number|null} - ID de la liste ou null
     */
    getListIdFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/dashboard\/reading-lists\/(\d+)/);
        return match ? parseInt(match[1]) : null;
    },

    /**
     * Retire un livre d'une liste
     * @param {number} listId - ID de la liste
     * @param {number} bookId - ID du livre
     */
    async removeBookFromList(listId, bookId) {
        try {
            await API.removeBookFromList(listId, bookId);
            // Supprimer l'élément du DOM
            const bookCard = document.querySelector(`.book-card [data-list-id="${listId}"][data-book-id="${bookId}"]`).closest('.book-card');
            bookCard.remove();

            // Si nous sommes sur la page de détails d'une liste et qu'il n'y a plus de livres
            const booksGrid = document.querySelector('.books-grid');
            if (booksGrid && booksGrid.children.length === 0) {
                const container = document.getElementById('list-books-container');
                container.innerHTML = `
                    <div class="empty-state">
                        <p>Aucun livre dans cette liste.</p>
                        <a href="/books" class="btn btn-secondary">Explorer le catalogue</a>
                    </div>
                `;
            }
        } catch (error) {
            alert(`Erreur: ${error.message}`);
        }
    }
};

export default ReadingListsManager;
