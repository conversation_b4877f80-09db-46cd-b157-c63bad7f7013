<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Ajouter à une liste</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <h1>Test - Fonctionnalité "Ajouter à une liste"</h1>
    
    <div class="test-section">
        <h2>Test 1: Bouton sur page de détails de livre</h2>
        <p>Simuler le bouton "Ajouter à ma liste" sur une page de détails de livre</p>
        <button class="btn btn-primary" id="addToListBtn">
            <i class="fas fa-plus"></i> Ajouter à ma liste
        </button>
    </div>

    <div class="test-section">
        <h2>Test 2: API Endpoints</h2>
        <p>Tester les appels API</p>
        <button class="btn btn-secondary" onclick="testGetLists()">
            <i class="fas fa-list"></i> Tester récupération des listes
        </button>
        <button class="btn btn-secondary" onclick="testAddToList()">
            <i class="fas fa-plus"></i> Tester ajout à une liste
        </button>
        <div id="api-results" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;"></div>
    </div>

    <!-- Modal pour ajouter à une liste -->
    <div class="modal" id="addToListModal">
        <div class="modal-content">
            <span class="close" id="closeAddToListModal">&times;</span>
            <h3>Ajouter à une liste de lecture</h3>
            
            <div id="addToListContainer">
                <!-- Les listes seront chargées dynamiquement ici -->
                <div class="loading-spinner" id="listsLoadingSpinner">
                    <i class="fas fa-spinner fa-spin"></i> Chargement des listes...
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="cancelAddToListBtn">Fermer</button>
            </div>
        </div>
    </div>

    <script>
        // Simuler l'environnement de l'application
        window.location.pathname = '/books/1'; // Simuler qu'on est sur la page du livre ID 1

        // Fonctions de test pour les API
        async function testGetLists() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test en cours...';
            
            try {
                const response = await fetch('/api/reading-lists');
                const data = await response.json();
                resultsDiv.innerHTML = `
                    <strong>✅ Succès - Récupération des listes:</strong><br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <strong>❌ Erreur - Récupération des listes:</strong><br>
                    ${error.message}
                `;
            }
        }

        async function testAddToList() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test en cours...';
            
            try {
                const response = await fetch('/api/reading-lists/add-book', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ list_id: 1, book_id: 1 })
                });
                const data = await response.json();
                resultsDiv.innerHTML = `
                    <strong>✅ Succès - Ajout à la liste:</strong><br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <strong>❌ Erreur - Ajout à la liste:</strong><br>
                    ${error.message}
                `;
            }
        }

        // Charger les scripts de l'application
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page de test chargée');
            
            // Simuler le chargement des modules
            import('/frontend/js/components/reading-lists.js')
                .then(module => {
                    console.log('Module reading-lists chargé:', module);
                    if (module.default && module.default.init) {
                        module.default.init();
                        console.log('ReadingListsManager initialisé');
                    }
                })
                .catch(error => {
                    console.error('Erreur lors du chargement du module:', error);
                });
        });
    </script>
</body>
</html>
